import Link from "next/link";

interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  date: string;
  author: {
    name: string;
    role: string;
  };
  category: string;
  readTime: string;
  imageUrl: string;
}

export default function BlogPage() {
  // Sample blog posts data
  const blogPosts: BlogPost[] = [
    {
      id: "web-development-trends-2025",
      title: "Web Development Trends to Watch in 2025",
      excerpt: "Explore the latest web development technologies and methodologies that are shaping the digital landscape in 2025, from AI-driven development to advanced frontend frameworks.",
      date: "March 15, 2025",
      author: {
        name: "<PERSON>",
        role: "Founder & CEO"
      },
      category: "Web Development",
      readTime: "5 min read",
      imageUrl: "/blog-placeholder1.jpg"
    },
    {
      id: "building-scalable-applications",
      title: "Building Scalable Applications with Microservices",
      excerpt: "Learn how microservices architecture can help you build more scalable, maintainable, and resilient applications, with real-world examples and best practices.",
      date: "February 28, 2025",
      author: {
        name: "<PERSON>",
        role: "Lead Developer"
      },
      category: "Software Architecture",
      readTime: "8 min read",
      imageUrl: "/blog-placeholder2.jpg"
    },
    {
      id: "ux-design-principles",
      title: "Essential UX Design Principles for Better User Engagement",
      excerpt: "Discover key UX design principles that can significantly improve user engagement and satisfaction, backed by research and case studies from our recent projects.",
      date: "February 10, 2025",
      author: {
        name: "Michael Chen",
        role: "UI/UX Designer"
      },
      category: "Design",
      readTime: "6 min read",
      imageUrl: "/blog-placeholder3.jpg"
    },
    {
      id: "ai-in-web-development",
      title: "How AI is Transforming Web Development",
      excerpt: "An in-depth look at how artificial intelligence is changing the web development landscape, from automated coding to intelligent user experiences.",
      date: "January 25, 2025",
      author: {
        name: "Emily Rodriguez",
        role: "Project Manager"
      },
      category: "Technology",
      readTime: "7 min read",
      imageUrl: "/blog-placeholder4.jpg"
    },
    {
      id: "optimizing-website-performance",
      title: "Advanced Techniques for Optimizing Website Performance",
      excerpt: "Explore advanced strategies and tools for improving website speed, responsiveness, and overall performance to enhance user experience and SEO rankings.",
      date: "January 12, 2025",
      author: {
        name: "David Kim",
        role: "Backend Developer"
      },
      category: "Performance",
      readTime: "9 min read",
      imageUrl: "/blog-placeholder5.jpg"
    },
    {
      id: "mobile-first-development",
      title: "The Importance of Mobile-First Development in 2025",
      excerpt: "Why mobile-first development remains crucial in 2025 and how to implement effective strategies that ensure optimal experiences across all devices.",
      date: "December 30, 2024",
      author: {
        name: "Lisa Patel",
        role: "Frontend Developer"
      },
      category: "Mobile Development",
      readTime: "5 min read",
      imageUrl: "/blog-placeholder6.jpg"
    },
  ];

  // Categories for filtering
  const categories = Array.from(new Set(blogPosts.map(post => post.category)));

  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 md:py-28 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl sm:text-5xl font-bold tracking-tight animate-fade-in">
              Our <span className="text-blue-600 dark:text-blue-400">Blog</span>
            </h1>
            <p className="mt-6 text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto animate-slide-up">
              Insights, tutorials, and updates from the Hiel Tech team
            </p>
          </div>
        </div>
      </section>

      {/* Blog Posts */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          {/* Category Filters */}
          <div className="mb-12 flex flex-wrap justify-center gap-4">
            <button className="px-4 py-2 rounded-full bg-blue-600 text-white font-medium hover:bg-blue-700 transition-colors">
              All Posts
            </button>
            {categories.map((category, index) => (
              <button 
                key={index}
                className="px-4 py-2 rounded-full border border-gray-300 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
              >
                {category}
              </button>
            ))}
          </div>
          
          {/* Featured Post */}
          <div className="mb-16">
            <div className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-md">
              <div className="md:flex">
                <div className="md:w-1/2 h-64 md:h-auto bg-gray-200 dark:bg-gray-700 relative">
                  {/* Replace with actual featured post image */}
                  <div className="absolute inset-0 flex items-center justify-center text-gray-500 dark:text-gray-400 text-xl font-semibold">
                    Featured Post Image
                  </div>
                </div>
                <div className="md:w-1/2 p-8">
                  <div className="flex items-center mb-4">
                    <span className="text-sm font-medium text-blue-600 dark:text-blue-400">{blogPosts[0].category}</span>
                    <span className="mx-2 text-gray-400">•</span>
                    <span className="text-sm text-gray-500 dark:text-gray-400">{blogPosts[0].date}</span>
                    <span className="mx-2 text-gray-400">•</span>
                    <span className="text-sm text-gray-500 dark:text-gray-400">{blogPosts[0].readTime}</span>
                  </div>
                  <h2 className="text-2xl font-bold mb-4">{blogPosts[0].title}</h2>
                  <p className="text-gray-600 dark:text-gray-300 mb-6">{blogPosts[0].excerpt}</p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="w-10 h-10 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center text-gray-500 dark:text-gray-400 mr-3">
                        {blogPosts[0].author.name.split(' ').map(n => n[0]).join('')}
                      </div>
                      <div>
                        <p className="font-medium">{blogPosts[0].author.name}</p>
                        <p className="text-sm text-gray-500 dark:text-gray-400">{blogPosts[0].author.role}</p>
                      </div>
                    </div>
                    <Link 
                      href={`/blog/${blogPosts[0].id}`}
                      className="px-4 py-2 rounded-full bg-blue-600 text-white font-medium hover:bg-blue-700 transition-colors"
                    >
                      Read More
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Blog Posts Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {blogPosts.slice(1).map((post) => (
              <div 
                key={post.id}
                className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 flex flex-col"
              >
                {/* Post Image */}
                <div className="h-48 bg-gray-200 dark:bg-gray-700 relative">
                  {/* Replace with actual post images */}
                  <div className="absolute inset-0 flex items-center justify-center text-gray-500 dark:text-gray-400">
                    Blog Post Image
                  </div>
                </div>
                
                {/* Post Info */}
                <div className="p-6 flex-grow">
                  <div className="flex items-center mb-3">
                    <span className="text-xs font-medium text-blue-600 dark:text-blue-400">{post.category}</span>
                    <span className="mx-2 text-gray-400">•</span>
                    <span className="text-xs text-gray-500 dark:text-gray-400">{post.readTime}</span>
                  </div>
                  
                  <h3 className="text-xl font-semibold mb-3">{post.title}</h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-4 line-clamp-3">{post.excerpt}</p>
                </div>
                
                {/* Author and Date */}
                <div className="px-6 pb-6 mt-auto">
                  <div className="pt-4 border-t border-gray-200 dark:border-gray-700 flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center text-gray-500 dark:text-gray-400 text-xs mr-2">
                        {post.author.name.split(' ').map(n => n[0]).join('')}
                      </div>
                      <div>
                        <p className="text-sm font-medium">{post.author.name}</p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">{post.date}</p>
                      </div>
                    </div>
                    <Link 
                      href={`/blog/${post.id}`}
                      className="text-blue-600 dark:text-blue-400 hover:underline text-sm"
                    >
                      Read →
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          {/* Pagination */}
          <div className="mt-16 flex justify-center">
            <nav className="flex items-center space-x-2">
              <button className="px-3 py-1 rounded border border-gray-300 dark:border-gray-700 text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
                Previous
              </button>
              <button className="px-3 py-1 rounded bg-blue-600 text-white">1</button>
              <button className="px-3 py-1 rounded border border-gray-300 dark:border-gray-700 text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
                2
              </button>
              <button className="px-3 py-1 rounded border border-gray-300 dark:border-gray-700 text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
                3
              </button>
              <button className="px-3 py-1 rounded border border-gray-300 dark:border-gray-700 text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
                Next
              </button>
            </nav>
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900/50">
        <div className="container mx-auto">
          <div className="max-w-2xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-4">Subscribe to Our Newsletter</h2>
            <p className="text-gray-600 dark:text-gray-300 mb-8">
              Stay updated with our latest insights, tutorials, and industry news
            </p>
            <form className="flex flex-col sm:flex-row gap-4">
              <input 
                type="email" 
                placeholder="Enter your email" 
                className="flex-grow px-4 py-3 rounded-full border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-600 dark:focus:ring-blue-500"
                required
              />
              <button 
                type="submit"
                className="px-6 py-3 rounded-full bg-blue-600 text-white font-medium hover:bg-blue-700 transition-colors"
              >
                Subscribe
              </button>
            </form>
            <p className="mt-4 text-sm text-gray-500 dark:text-gray-400">
              We respect your privacy. Unsubscribe at any time.
            </p>
          </div>
        </div>
      </section>
    </div>
  );
}
